// ==UserScript==
// @name         GitHub自动注册
// @namespace    http://tampermonkey.net/
// @version      3.8
// @description  自动完成GitHub的注册流程，自动获取验证码，包含账号库管理，支持neoz.ltd和zhou1.shop系列域名，智能仿人类账号生成，优化存储容量，账号导出下载，历史账号库，分页版界面
// <AUTHOR> JOU
// @match        https://github.com/*
// @match        https://tempmail.plus/*
// @icon         https://github.com/favicon.ico
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_addStyle
// @grant        GM_setClipboard
// @grant        window.close
// @connect      tempmail.plus
// @run-at       document-idle
// ==/UserScript==

(function() {
    'use strict';

    // ======================== 配置区域 ========================
    const CONFIG = {
        // GitHub账号配置
        GITHUB: {
            // 中文拼音词汇
            CHINESE_WORDS: [
                'xiaoming', 'xiaohong', 'xiaohua', 'xiaoli', 'xiaogang', 'xiaofang',
                'laowang', 'laoli', 'laozhang', 'laoliu', 'laochen', 'laoma',
                'zhangsan', 'lisi', 'wangwu', 'zhaoliu', 'sunqi', 'zhouba',
                'meimei', 'gege', 'didi', 'jiejie', 'baba', 'mama',
                'xiaoyu', 'xiaoxue', 'xiaobing', 'xiaofei', 'xiaojun', 'xiaokang',
                'dawei', 'dahua', 'daming', 'daqiang', 'dagang', 'dapeng',
                'aixin', 'baobei', 'caicai', 'dongdong', 'fanfan', 'guoguo',
                'huihui', 'jiajia', 'keke', 'lele', 'mingming', 'nannan',
                'qiqi', 'rongrong', 'sisi', 'tingting', 'weiwei', 'xinxin',
                'yangyang', 'zhuzhu', 'linlin', 'feifei', 'taotao', 'yueyue'
            ],
            // 英文常见词汇
            ENGLISH_WORDS: [
                'john', 'mike', 'david', 'james', 'robert', 'william', 'richard', 'thomas',
                'mary', 'patricia', 'jennifer', 'linda', 'elizabeth', 'barbara', 'susan', 'jessica',
                'alex', 'chris', 'sam', 'max', 'ben', 'tom', 'joe', 'ryan',
                'amy', 'lisa', 'anna', 'emma', 'sarah', 'kate', 'jane', 'rose',
                'admin', 'user', 'guest', 'member', 'client', 'owner', 'manager', 'leader',
                'dev', 'coder', 'hacker', 'geek', 'ninja', 'master', 'expert', 'pro',
                'cool', 'smart', 'quick', 'fast', 'best', 'super', 'top', 'star',
                'love', 'happy', 'lucky', 'dream', 'hope', 'wish', 'magic', 'wonder',
                'blue', 'red', 'green', 'black', 'white', 'gold', 'silver', 'purple',
                'sun', 'moon', 'star', 'sky', 'sea', 'wind', 'fire', 'earth'
            ],
            // 技术相关词汇
            TECH_WORDS: [
                'github', 'gitlab', 'bitbucket', 'code', 'repo', 'commit', 'branch', 'merge',
                'python', 'java', 'javascript', 'react', 'vue', 'angular', 'node', 'express',
                'docker', 'kubernetes', 'aws', 'azure', 'cloud', 'api', 'rest', 'graphql',
                'database', 'mysql', 'mongodb', 'redis', 'nginx', 'apache', 'linux', 'ubuntu',
                'frontend', 'backend', 'fullstack', 'devops', 'mobile', 'web', 'app', 'game'
            ],
            EMAIL_DOMAINS_NEOZ: [
                '@neoz.ltd',
                '@adidas.neoz.ltd',
                '@balen.neoz.ltd',
                '@dahua.neoz.ltd',
                '@tita.neoz.ltd',
                '@hot.neoz.ltd',
                '@tian1.neoz.ltd',
                '@apple.neoz.ltd',
                '@xiaoshuo.neoz.ltd'
            ],
            EMAIL_DOMAINS_ZHOU1: [
                '@zhou1.shop',
                '@886.zhou1.shop',
                '@ai.zhou1.shop',
                '@tencent.zhou1.shop',
                '@xyz.zhou1.shop',
                '@feng.zhou1.shop',
                '@huawei.zhou1.shop',
                '@bmw.zhou1.shop',
                '@china.zhou1.shop'
            ]
        },
        // TempMail配置
        TEMPMAIL: {
            USERNAME: 'neo888',
            DOMAIN: '@mailto.plus',
            PIN: '0418',
            API_BASE: 'https://tempmail.plus/api'
        },
        // 重试配置
        RETRY: {
            MAX_ATTEMPTS: 10,
            INTERVAL: 3000,
            EMAIL_CHECK_INTERVAL: 5000
        },
        // 调试模式
        DEBUG: true
    };

    // ======================== 工具函数 ========================

    // 日志输出
    function log(message, type = 'info') {
        if (!CONFIG.DEBUG && type === 'debug') return;

        const prefix = '[GitHub自动注册]';
        const timestamp = new Date().toLocaleTimeString();

        switch(type) {
            case 'success':
                console.log(`%c${prefix} [${timestamp}] ✓ ${message}`, 'color: #4CAF50; font-weight: bold;');
                break;
            case 'error':
                console.error(`%c${prefix} [${timestamp}] ✗ ${message}`, 'color: #f44336; font-weight: bold;');
                break;
            case 'warning':
                console.warn(`%c${prefix} [${timestamp}] ⚠ ${message}`, 'color: #ff9800; font-weight: bold;');
                break;
            case 'debug':
                console.log(`%c${prefix} [${timestamp}] 🐛 ${message}`, 'color: #9C27B0;');
                break;
            default:
                console.log(`%c${prefix} [${timestamp}] ${message}`, 'color: #2196F3;');
        }
    }

    // 等待元素出现
    async function waitForElement(selector, timeout = 10000) {
        log(`等待元素: ${selector}`, 'debug');
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) {
                log(`找到元素: ${selector}`, 'debug');
                return element;
            }
            await sleep(100);
        }

        log(`元素未找到: ${selector}`, 'warning');
        return null;
    }

    // 等待多个元素中的任意一个
    async function waitForAnyElement(selectors, timeout = 10000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) {
                    return { element, selector };
                }
            }
            await sleep(100);
        }

        return null;
    }

    // 延迟函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 生成随机数
    function getRandomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 从数组中随机选择
    function getRandomFromArray(arr) {
        return arr[Math.floor(Math.random() * arr.length)];
    }

    // 生成随机字符串
    function generateRandomString(length) {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 生成随机邮箱 - 多样化有意义前缀
    function generateEmail() {
        // 合并所有词汇库
        const allWords = [
            ...CONFIG.GITHUB.CHINESE_WORDS,
            ...CONFIG.GITHUB.ENGLISH_WORDS,
            ...CONFIG.GITHUB.TECH_WORDS
        ];

        // 随机选择生成样式 (10种不同样式)
        const styles = [
            // 样式1: 单词 + 数字 (如: xiaoming123)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 9999);
                return `${word}${num}`;
            },
            // 样式2: 单词 + 年份 (如: mike2024)
            () => {
                const word = getRandomFromArray(allWords);
                const year = getRandomNumber(1990, 2025);
                return `${word}${year}`;
            },
            // 样式3: 数字 + 单词 (如: 88xiaoli)
            () => {
                const num = getRandomNumber(1, 999);
                const word = getRandomFromArray(allWords);
                return `${num}${word}`;
            },
            // 样式4: 单词 + 下划线 + 数字 (如: david_123)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 999);
                return `${word}_${num}`;
            },
            // 样式5: 两个单词组合 (如: happystar)
            () => {
                const word1 = getRandomFromArray(allWords);
                const word2 = getRandomFromArray(allWords);
                return `${word1}${word2}`;
            },
            // 样式6: 单词 + 点 + 数字 (如: lisa.456)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 999);
                return `${word}.${num}`;
            },
            // 样式7: 单词 + 随机字母 (如: john87a)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(10, 99);
                const letter = String.fromCharCode(97 + Math.floor(Math.random() * 26));
                return `${word}${num}${letter}`;
            },
            // 样式8: my + 单词 + 数字 (如: myhappy123)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 999);
                return `my${word}${num}`;
            },
            // 样式9: 单词 + love + 数字 (如: marylove520)
            () => {
                const word = getRandomFromArray(CONFIG.GITHUB.CHINESE_WORDS.concat(CONFIG.GITHUB.ENGLISH_WORDS.slice(0, 20)));
                const num = getRandomNumber(1, 999);
                return `${word}love${num}`;
            },
            // 样式10: 纯单词 (如: javascript)
            () => {
                return getRandomFromArray(allWords);
            }
        ];

        // 随机选择一种样式生成前缀
        const selectedStyle = getRandomFromArray(styles);
        const prefix = selectedStyle();

        // 将所有域名合并为一个数组
        const allDomains = [...CONFIG.GITHUB.EMAIL_DOMAINS_NEOZ, ...CONFIG.GITHUB.EMAIL_DOMAINS_ZHOU1];

        // 完全随机选择一个域名
        const selectedDomain = getRandomFromArray(allDomains);

        log(`生成邮箱前缀: ${prefix}, 域名: ${selectedDomain}`, 'debug');

        return `${prefix}${selectedDomain}`;
    }

    // 生成随机用户名 - 严格符合GitHub规则
    function generateUsername() {
        // 合并所有词汇库
        const allWords = [
            ...CONFIG.GITHUB.CHINESE_WORDS,
            ...CONFIG.GITHUB.ENGLISH_WORDS,
            ...CONFIG.GITHUB.TECH_WORDS
        ];

        // 随机选择生成样式 - 严格符合GitHub规则（只能包含字母数字和连字符）
        const styles = [
            // 样式1: 单词 + 数字 (如: xiaoming123)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 999);
                return `${word}${num}`;
            },
            // 样式2: 单词 + 年份后两位 (如: mike24)
            () => {
                const word = getRandomFromArray(allWords);
                const year = getRandomNumber(90, 25);
                return `${word}${year}`;
            },
            // 样式3: 单词 + 连字符 + 数字 (如: david-123)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 99);
                return `${word}-${num}`;
            },
            // 样式4: 两个单词连字符连接 (如: happy-star)
            () => {
                const word1 = getRandomFromArray(allWords);
                const word2 = getRandomFromArray(allWords);
                return `${word1}-${word2}`;
            },
            // 样式5: 单词 + 数字 + 字母 (如: john87a)
            () => {
                const word = getRandomFromArray(allWords);
                const num = getRandomNumber(1, 99);
                const letter = String.fromCharCode(97 + Math.floor(Math.random() * 26));
                return `${word}${num}${letter}`;
            },
            // 样式6: 纯单词 (如: javascript)
            () => {
                return getRandomFromArray(allWords);
            },
            // 样式7: 单词 + 特殊数字 (如: lucky520, love888)
            () => {
                const word = getRandomFromArray(allWords);
                const specialNums = [520, 888, 666, 999, 123, 456, 789];
                const num = getRandomFromArray(specialNums);
                return `${word}${num}`;
            },
            // 样式8: 连字符分隔的单词数字组合 (如: tech-dev-2024)
            () => {
                const word1 = getRandomFromArray(allWords);
                const word2 = getRandomFromArray(['dev', 'user', 'pro', 'master', 'king', 'star', 'top', 'best']);
                const num = getRandomNumber(2020, 2025);
                return `${word1}-${word2}-${num}`;
            }
        ];

        let username = '';
        let attempts = 0;
        
        do {
            // 随机选择一种样式生成用户名
            const selectedStyle = getRandomFromArray(styles);
            username = selectedStyle();
            
            // 确保用户名符合GitHub严格规则
            // 1. 只能包含字母数字和连字符
            username = username.replace(/[^a-zA-Z0-9-]/g, '');
            
            // 2. 不能以连字符开头或结尾
            username = username.replace(/^-+|-+$/g, '');
            
            // 3. 连字符不能连续出现
            username = username.replace(/-+/g, '-');
            
            // 4. 不能以数字开头
            if (/^\d/.test(username)) {
                const letters = 'abcdefghijklmnopqrstuvwxyz';
                username = letters.charAt(Math.floor(Math.random() * letters.length)) + username;
            }
            
            // 5. 确保用户名长度合适（GitHub用户名最长39个字符，最短1个字符）
            if (username.length > 39) {
                username = username.substring(0, 39);
            }
            
            // 6. 再次检查结尾不是连字符
            username = username.replace(/-+$/, '');
            
            attempts++;
        } while ((username.length < 1 || !/^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$|^[a-zA-Z]$/.test(username)) && attempts < 10);
        
        // 如果多次尝试都不成功，使用一个简单的后备方案
        if (username.length < 1 || !/^[a-zA-Z][a-zA-Z0-9-]*[a-zA-Z0-9]$|^[a-zA-Z]$/.test(username)) {
            const word = getRandomFromArray(allWords);
            const num = getRandomNumber(1, 999);
            username = `${word}${num}`;
            // 确保以字母开头
            if (/^\d/.test(username)) {
                username = 'u' + username;
            }
        }
        
        log(`生成用户名: ${username}`, 'debug');
        return username;
    }

    // 生成随机密码 - 15个字符，包含大小写字母和数字
    function generatePassword() {
        // 使用大小写字母、数字，避免容易混淆的字符(0,O,1,l,I等)
        const chars = 'abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        let password = '';
        for (let i = 0; i < 15; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        log(`生成随机密码: ${password}`, 'debug');
        return password;
    }

    // 直接填充输入框
    function directFillInput(element, text) {
        element.focus();
        element.value = text;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.blur();
    }

    // 尝试多种点击方式
    async function tryMultipleClicks(element) {
        const methods = [
            () => element.click(),
            () => element.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true })),
            () => clickElement(element),
            () => {
                const event = new Event('submit', { bubbles: true, cancelable: true });
                const form = element.closest('form');
                if (form) {
                    form.dispatchEvent(event);
                } else {
                    element.dispatchEvent(event);
                }
            },
            () => {
                element.focus();
                element.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', code: 'Enter', bubbles: true }));
                element.dispatchEvent(new KeyboardEvent('keypress', { key: 'Enter', code: 'Enter', bubbles: true }));
                element.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', code: 'Enter', bubbles: true }));
            }
        ];

        for (let i = 0; i < methods.length; i++) {
            try {
                log(`尝试点击方式 ${i + 1}/${methods.length}`, 'debug');
                await methods[i]();
                await sleep(500);

                // 检查是否有页面变化或状态变化
                const currentUrl = window.location.href;
                const hasVerificationElements = document.querySelector('input[type="text"][maxlength="6"], input[type="text"][maxlength="8"], input[name*="code"], input[placeholder*="code"]');

                if (currentUrl !== window.location.href || hasVerificationElements) {
                    log(`点击方式 ${i + 1} 成功`, 'success');
                    return true;
                }
            } catch (error) {
                log(`点击方式 ${i + 1} 失败: ${error.message}`, 'warning');
            }
        }

        return false;
    }

    // 点击元素
    async function clickElement(element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await sleep(500);

        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;

        element.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));
        await sleep(100);

        element.dispatchEvent(new MouseEvent('mousedown', {
            bubbles: true,
            clientX: x,
            clientY: y
        }));

        await sleep(50);

        element.dispatchEvent(new MouseEvent('mouseup', {
            bubbles: true,
            clientX: x,
            clientY: y
        }));

        element.dispatchEvent(new MouseEvent('click', {
            bubbles: true,
            clientX: x,
            clientY: y
        }));
    }

    // ======================== 账号库管理 ========================

    // 获取账号库
    function getAccountLibrary() {
        const library = GM_getValue('account_library', []);
        // 确保返回的是数组格式，防止数据损坏
        return Array.isArray(library) ? library : [];
    }

    // 保存账号到库
    function saveAccountToLibrary(email, username, password) {
        const library = getAccountLibrary();
        // 使用更精确的ID生成，避免冲突
        const newAccount = {
            id: Date.now() + Math.random().toString(36).substring(2, 11),
            email: email,
            username: username,
            password: password,
            createdAt: new Date().toLocaleString('zh-CN'),
            registrationSuccess: false,
            verificationStatus: 'pending', // pending, valid, invalid, verifying
            lastVerified: null
        };
        library.push(newAccount);

        // 确保存储成功
        try {
            GM_setValue('account_library', library);
            log(`账号已保存到库: ${email}`, 'success');
        } catch (error) {
            log(`保存账号失败: ${error}`, 'error');
        }

        return newAccount.id;
    }

    // 更新账号状态
    function updateAccountStatus(id, success) {
        const library = getAccountLibrary();
        const account = library.find(acc => acc.id === id);
        if (account) {
            account.registrationSuccess = success;
            try {
                GM_setValue('account_library', library);
                log(`账号状态已更新: ${account.email} -> ${success ? '成功' : '待验证'}`, 'success');
            } catch (error) {
                log(`更新账号状态失败: ${error}`, 'error');
            }
        }
    }

    // 删除账号
    function deleteAccountFromLibrary(id) {
        const library = getAccountLibrary();
        const newLibrary = library.filter(acc => acc.id !== id);
        try {
            GM_setValue('account_library', newLibrary);
            log(`账号已删除`, 'success');
        } catch (error) {
            log(`删除账号失败: ${error}`, 'error');
        }
    }

    // 导出所有账号（文件下载格式）
    function exportAllAccounts() {
        const library = getAccountLibrary();

        if (library.length === 0) {
            alert('没有账号可以导出！');
            return;
        }

        // 生成简洁格式的文本
        const exportText = library.map(acc => `${acc.email} ${acc.password}`).join('\n');

        // 生成文件名
        const now = new Date();
        const dateStr = now.getFullYear().toString() +
                       (now.getMonth() + 1).toString().padStart(2, '0') +
                       now.getDate().toString().padStart(2, '0');
        const timeStr = now.getHours().toString().padStart(2, '0') +
                       now.getMinutes().toString().padStart(2, '0') +
                       now.getSeconds().toString().padStart(2, '0');
        const filename = `github-accounts-${dateStr}-${timeStr}.txt`;

        // 创建下载链接
        const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 添加到历史账号库
        addToHistoryLibrary(library);

        // 显示导出信息
        const exportInfo = `已导出 ${library.length} 个账号到文件：${filename}\n\n格式预览：\n${exportText.split('\n').slice(0, 3).join('\n')}${library.length > 3 ? '\n...' : ''}`;

        if (confirm(`${exportInfo}\n\n是否要清空已导出的账号记录？`)) {
            clearExportedAccounts();
        }

        log(`导出了 ${library.length} 个账号到文件: ${filename}`, 'success');
    }

    // 清空已导出的账号
    function clearExportedAccounts() {
        const library = getAccountLibrary();
        // 由于现在导出所有账号，清空时也清空所有
        const remainingAccounts = [];

        try {
            GM_setValue('account_library', remainingAccounts);
            log(`已清空 ${library.length} 个已导出的账号`, 'success');

            // 刷新账号库显示
            if (document.querySelector('.account-library-modal.show')) {
                showAccountLibrary();
            }
        } catch (error) {
            log(`清空账号失败: ${error}`, 'error');
        }
    }

    // ======================== 历史账号库管理 ========================

    // 获取历史账号库
    function getHistoryAccountLibrary() {
        const library = GM_getValue('history_account_library', []);
        return Array.isArray(library) ? library : [];
    }

    // 添加账号到历史库
    function addToHistoryLibrary(accounts) {
        const historyLibrary = getHistoryAccountLibrary();
        const currentTime = new Date().toLocaleString('zh-CN');

        const historyAccounts = accounts.map(acc => ({
            id: Date.now() + Math.random().toString(36).substring(2, 11),
            email: acc.email,
            password: acc.password,
            exportedAt: currentTime,
            verificationStatus: acc.verificationStatus || 'pending',
            lastVerified: acc.lastVerified || null
        }));

        historyLibrary.push(...historyAccounts);

        try {
            GM_setValue('history_account_library', historyLibrary);
            log(`已添加 ${historyAccounts.length} 个账号到历史库`, 'success');
        } catch (error) {
            log(`添加到历史库失败: ${error}`, 'error');
        }
    }

    // 从历史库删除账号
    function deleteFromHistoryLibrary(id) {
        const historyLibrary = getHistoryAccountLibrary();
        const newLibrary = historyLibrary.filter(acc => acc.id !== id);

        try {
            GM_setValue('history_account_library', newLibrary);
            log(`历史账号已删除`, 'success');
        } catch (error) {
            log(`删除历史账号失败: ${error}`, 'error');
        }
    }

    // 批量删除历史账号
    function batchDeleteFromHistoryLibrary(ids) {
        const historyLibrary = getHistoryAccountLibrary();
        const newLibrary = historyLibrary.filter(acc => !ids.includes(acc.id));

        try {
            GM_setValue('history_account_library', newLibrary);
            log(`已批量删除 ${ids.length} 个历史账号`, 'success');
        } catch (error) {
            log(`批量删除历史账号失败: ${error}`, 'error');
        }
    }

    // 清空当前账号库
    function clearCurrentAccountLibrary() {
        const library = getAccountLibrary();
        if (library.length === 0) {
            alert('当前账号库已经是空的！');
            return false;
        }

        if (confirm(`确定要清空当前账号库中的所有 ${library.length} 个账号吗？此操作不可恢复！\n\n建议先导出成功的账号。`)) {
            try {
                GM_setValue('account_library', []);
                log(`已清空当前账号库，删除了 ${library.length} 个账号`, 'success');
                return true;
            } catch (error) {
                log(`清空当前账号库失败: ${error}`, 'error');
                alert('清空账号库失败，请重试');
                return false;
            }
        }
        return false;
    }

    // 清空历史账号库
    function clearHistoryLibrary() {
        try {
            GM_setValue('history_account_library', []);
            log(`历史账号库已清空`, 'success');
        } catch (error) {
            log(`清空历史账号库失败: ${error}`, 'error');
        }
    }

    // ======================== 账号验证功能 ========================

    // 验证单个GitHub账号 - 改进版本
    async function verifyGitHubAccount(account, isHistory = false) {
        // 更新验证状态为验证中
        updateAccountVerificationStatus(account.id, 'verifying', isHistory);

        return new Promise((resolve) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://github.com/session',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Referer': 'https://github.com/login',
                    'Origin': 'https://github.com',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'same-origin'
                },
                data: `login=${encodeURIComponent(account.email)}&password=${encodeURIComponent(account.password)}&commit=Sign+in&return_to=&webauthn-support=supported&webauthn-iuvpaa-support=unsupported&timestamp=${Date.now()}&timestamp_secret=`,
                timeout: 15000,
                onload: (response) => {
                    let status = 'invalid';
                    let isValid = false;
                    let reason = '登录失败';

                    try {
                        log(`验证响应状态: ${response.status}, finalUrl: ${response.finalUrl}`, 'debug');
                        
                        // 方法1: 检查HTTP重定向状态码 (最可靠)
                        if (response.status === 302 || (response.finalUrl && response.finalUrl !== 'https://github.com/session')) {
                            if (response.finalUrl && 
                                response.finalUrl.includes('github.com') && 
                                !response.finalUrl.includes('login') &&
                                !response.finalUrl.includes('session')) {
                                isValid = true;
                                status = 'valid';
                                reason = '重定向验证成功';
                                log(`重定向验证成功: ${response.finalUrl}`, 'success');
                            }
                        }

                        // 方法2: 检查Session Cookie
                        if (!isValid && response.responseHeaders) {
                            const cookieHeader = response.responseHeaders.toLowerCase();
                            if (cookieHeader.includes('set-cookie') && 
                                (cookieHeader.includes('user_session') || cookieHeader.includes('logged_in=yes'))) {
                                isValid = true;
                                status = 'valid';
                                reason = 'Cookie验证成功';
                                log('通过Cookie验证成功', 'success');
                            }
                        }

                        // 方法3: 检查响应内容 (fallback)
                        if (!isValid && response.status === 200) {
                            const responseText = response.responseText.toLowerCase();
                            
                            // 检查登录失败的明确标识
                            if (responseText.includes('incorrect username or password') ||
                                responseText.includes('authentication failed') ||
                                responseText.includes('sign in to github') ||
                                responseText.includes('login_field') ||
                                responseText.includes('session[login]')) {
                                reason = '用户名或密码错误';
                            }
                            // 检查需要验证码的情况
                            else if (responseText.includes('captcha') || 
                                    responseText.includes('verification_token')) {
                                status = 'requires_captcha';
                                reason = '需要验证码';
                                log(`账号需要验证码: ${account.email}`, 'warning');
                            }
                            // 检查需要2FA的情况
                            else if (responseText.includes('two-factor') || 
                                    responseText.includes('otp_attempt')) {
                                status = 'requires_2fa';
                                reason = '需要双因素认证';
                                log(`账号需要2FA: ${account.email}`, 'warning');
                            }
                            // 检查账号被暂停的情况
                            else if (responseText.includes('account has been suspended') ||
                                    responseText.includes('account is temporarily restricted')) {
                                status = 'suspended';
                                reason = '账号被暂停';
                                log(`账号被暂停: ${account.email}`, 'warning');
                            }
                            // 如果包含成功登录的标识
                            else if (responseText.includes('dashboard') ||
                                    responseText.includes('notifications') ||
                                    responseText.includes('user-profile-nav') ||
                                    responseText.includes('header-nav-current-user')) {
                                isValid = true;
                                status = 'valid';
                                reason = '内容验证成功';
                                log('通过内容验证成功', 'success');
                            }
                        }

                        // 方法4: 检查特殊错误状态
                        if (response.status === 429) {
                            status = 'rate_limited';
                            reason = '请求过于频繁';
                            log(`请求被限制: ${account.email}`, 'warning');
                        } else if (response.status >= 500) {
                            status = 'server_error';
                            reason = 'GitHub服务器错误';
                            log(`服务器错误: ${account.email}`, 'warning');
                        }

                    } catch (error) {
                        log(`验证过程出错: ${error.message}`, 'error');
                        status = 'error';
                        reason = `验证出错: ${error.message}`;
                    }

                    updateAccountVerificationStatus(account.id, status, isHistory);
                    log(`账号验证完成: ${account.email} - ${status} (${reason})`, isValid ? 'success' : 'warning');
                    resolve({ account, isValid, status, reason });
                },
                onerror: (error) => {
                    updateAccountVerificationStatus(account.id, 'network_error', isHistory);
                    log(`账号验证网络错误: ${account.email} - ${error}`, 'error');
                    resolve({ account, isValid: false, status: 'network_error', reason: '网络连接错误' });
                },
                ontimeout: () => {
                    updateAccountVerificationStatus(account.id, 'timeout', isHistory);
                    log(`账号验证超时: ${account.email}`, 'warning');
                    resolve({ account, isValid: false, status: 'timeout', reason: '请求超时' });
                }
            });
        });
    }

    // 更新账号验证状态
    function updateAccountVerificationStatus(id, status, isHistory = false) {
        const storageKey = isHistory ? 'history_account_library' : 'account_library';
        const library = GM_getValue(storageKey, []);
        const account = library.find(acc => acc.id === id);

        if (account) {
            account.verificationStatus = status;
            account.lastVerified = new Date().toLocaleString('zh-CN');

            try {
                GM_setValue(storageKey, library);
            } catch (error) {
                log(`更新验证状态失败: ${error}`, 'error');
            }
        }
    }

    // 批量验证账号 - 改进版本
    async function batchVerifyAccounts(accounts, isHistory = false, onProgress = null) {
        const results = [];
        const total = accounts.length;

        log(`开始批量验证 ${total} 个账号`, 'info');

        for (let i = 0; i < accounts.length; i++) {
            const account = accounts[i];

            // 更新进度
            if (onProgress) {
                onProgress(i + 1, total, account.email);
            }

            log(`验证账号 ${i + 1}/${total}: ${account.email}`, 'info');
            const result = await verifyGitHubAccount(account, isHistory);
            results.push(result);

            // 动态延迟策略
            if (i < accounts.length - 1) {
                let delay = 2000; // 基础延迟2秒
                
                // 根据上一个结果调整延迟
                if (result.status === 'rate_limited') {
                    delay = 10000; // 被限制时延迟10秒
                    log('检测到请求限制，延长等待时间', 'warning');
                } else if (result.status === 'server_error') {
                    delay = 5000; // 服务器错误时延迟5秒
                } else if (result.status === 'network_error' || result.status === 'timeout') {
                    delay = 3000; // 网络问题时延迟3秒
                }
                
                // 添加随机因子避免被检测
                delay += Math.random() * 1000;
                
                log(`等待 ${(delay/1000).toFixed(1)} 秒后继续...`, 'debug');
                await sleep(delay);
            }
        }

        // 统计结果
        const successCount = results.filter(r => r.isValid).length;
        const errorCounts = {};
        results.forEach(r => {
            if (!r.isValid) {
                errorCounts[r.status] = (errorCounts[r.status] || 0) + 1;
            }
        });

        log(`批量验证完成: 成功 ${successCount}/${total}`, 'success');
        if (Object.keys(errorCounts).length > 0) {
            log(`失败统计: ${JSON.stringify(errorCounts)}`, 'info');
        }

        return results;
    }

    // ======================== 分页功能 ========================

    // 创建分页控件
    function createPagination(totalItems, currentPage, pageSize, containerId, onPageChange) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const totalPages = Math.ceil(totalItems / pageSize);
        
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `<button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} data-page="${currentPage - 1}">上一页</button>`;

        // 页码按钮
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        // 确保显示至少5个页码（如果总页数>=5）
        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else {
                startPage = Math.max(1, endPage - 4);
            }
        }

        // 第一页
        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" data-page="1">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="pagination-info">...</span>`;
            }
        }

        // 页码
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</button>`;
        }

        // 最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="pagination-info">...</span>`;
            }
            paginationHTML += `<button class="pagination-btn" data-page="${totalPages}">${totalPages}</button>`;
        }

        // 下一页按钮
        paginationHTML += `<button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} data-page="${currentPage + 1}">下一页</button>`;

        // 页面信息
        paginationHTML += `<span class="pagination-info">第 ${currentPage} 页，共 ${totalPages} 页</span>`;

        container.innerHTML = paginationHTML;

        // 绑定点击事件
        container.querySelectorAll('.pagination-btn').forEach(btn => {
            if (!btn.disabled) {
                btn.addEventListener('click', () => {
                    const page = parseInt(btn.getAttribute('data-page'));
                    if (page && page !== currentPage) {
                        onPageChange(page);
                    }
                });
            }
        });
    }

    // 创建浮动控制面板
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'github-auto-register-panel';

        GM_addStyle(`
            #github-auto-register-panel {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 350px;
                max-height: 400px;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            #github-auto-register-panel.minimized {
                width: 300px;
                max-height: 65px;
            }

            #github-auto-register-panel.minimized .panel-body {
                display: none;
            }

            #github-auto-register-panel.minimized .panel-title {
                font-size: 13px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 180px;
            }

            #github-auto-register-panel.minimized .panel-header {
                padding: 10px 12px;
            }

            .minimized-status {
                display: none;
                font-size: 11px;
                color: rgba(255, 255, 255, 0.9);
                margin-top: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            #github-auto-register-panel.minimized .minimized-status {
                display: block;
            }

            .panel-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px 16px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
            }

            .panel-title {
                font-size: 14px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .panel-controls {
                display: flex;
                gap: 8px;
            }

            .panel-btn {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 4px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s;
            }

            .panel-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .panel-body {
                padding: 16px;
                max-height: 300px;
                overflow-y: auto;
            }

            .status-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;
            }

            .status-item:last-child {
                border-bottom: none;
            }

            .status-icon {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
            }

            .status-icon.success {
                background: #4CAF50;
                color: white;
            }

            .status-icon.error {
                background: #f44336;
                color: white;
            }

            .status-icon.pending {
                background: #FFC107;
                color: white;
            }

            .status-icon.processing {
                background: #2196F3;
                color: white;
                animation: pulse 1.5s infinite;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }

            .status-text {
                font-size: 13px;
                color: #333;
                flex: 1;
            }

            .status-time {
                font-size: 11px;
                color: #999;
            }

            .action-buttons {
                display: flex;
                gap: 8px;
                margin-top: 16px;
            }

            .action-btn {
                flex: 1;
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
            }

            .action-btn.primary {
                background: #667eea;
                color: white;
            }

            .action-btn.primary:hover {
                background: #5a67d8;
            }

            .action-btn.secondary {
                background: #e0e0e0;
                color: #333;
            }

            .action-btn.secondary:hover {
                background: #d0d0d0;
            }

            .action-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .info-section {
                background: #f5f5f5;
                border-radius: 6px;
                padding: 12px;
                margin-top: 12px;
                font-size: 12px;
            }

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
            }

            .info-item:last-child {
                margin-bottom: 0;
            }

            .info-label {
                color: #666;
            }

            .info-value {
                color: #333;
                font-weight: 500;
                font-family: monospace;
                flex: 1;
                margin: 0 8px;
            }

            .copy-btn {
                padding: 2px 8px;
                font-size: 11px;
                background: #e0e0e0;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .copy-btn:hover {
                background: #d0d0d0;
            }

            .copy-btn:active {
                background: #c0c0c0;
            }

            /* 账号库弹窗样式 */
            .account-library-modal {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 800px;
                max-width: 90vw;
                max-height: 90vh;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                z-index: 10001;
                display: none;
                flex-direction: column;
            }

            .account-library-modal.show {
                display: flex;
            }

            .modal-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 16px 20px;
                border-radius: 12px 12px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-shrink: 0;
            }

            .modal-tabs {
                display: flex;
                background: #f5f5f5;
                border-bottom: 1px solid #e0e0e0;
                flex-shrink: 0;
            }

            .modal-tab {
                flex: 1;
                padding: 12px 20px;
                background: none;
                border: none;
                cursor: pointer;
                font-size: 14px;
                color: #666;
                transition: all 0.2s;
            }

            .modal-tab.active {
                background: white;
                color: #333;
                font-weight: 600;
            }

            .modal-tab:hover {
                background: #e8e8e8;
            }

            .modal-tab.active:hover {
                background: white;
            }

            .modal-body {
                padding: 20px;
                flex: 1;
                overflow-y: auto;
                min-height: 0;
            }

            .tab-content {
                display: none;
            }

            .tab-content.active {
                display: block;
            }

            .account-table {
                width: 100%;
                border-collapse: collapse;
            }

            .account-table th {
                background: #f5f5f5;
                padding: 10px;
                text-align: left;
                font-size: 13px;
                font-weight: 600;
                color: #666;
                border-bottom: 2px solid #e0e0e0;
            }

            .account-table td {
                padding: 12px 10px;
                font-size: 13px;
                border-bottom: 1px solid #f0f0f0;
            }

            .account-table tr:hover {
                background: #f9f9f9;
            }

            .account-status {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 500;
            }

            .account-status.success {
                background: #e8f5e9;
                color: #2e7d32;
            }

            .account-status.pending {
                background: #fff3e0;
                color: #f57c00;
            }

            .delete-account-btn {
                background: #f44336;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .delete-account-btn:hover {
                background: #d32f2f;
            }

            .modal-footer {
                padding: 16px 20px;
                border-top: 1px solid #e0e0e0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .modal-footer-buttons {
                display: flex;
                gap: 12px;
                align-items: center;
            }

            .account-count {
                font-size: 13px;
                color: #666;
            }

            .export-accounts-btn {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s;
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .export-accounts-btn:hover {
                background: #45a049;
            }

            .export-accounts-btn:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }

            .close-modal-btn {
                background: #667eea;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .close-modal-btn:hover {
                background: #5a67d8;
            }

            .verify-btn {
                background: #2196F3;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                cursor: pointer;
                transition: all 0.2s;
                margin-right: 4px;
            }

            .verify-btn:hover {
                background: #1976D2;
            }

            .verify-btn:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }

            .batch-verify-btn {
                background: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s;
                margin-right: 12px;
            }

            .batch-verify-btn:hover {
                background: #F57C00;
            }

            .batch-verify-btn:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }

            .verification-status {
                display: inline-block;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 10px;
                font-weight: 500;
                text-align: center;
                min-width: 40px;
            }

            .verification-status.pending {
                background: #fff3e0;
                color: #f57c00;
            }

            .verification-status.valid {
                background: #e8f5e9;
                color: #2e7d32;
            }

            .verification-status.invalid {
                background: #ffebee;
                color: #c62828;
            }

            .verification-status.verifying {
                background: #e3f2fd;
                color: #1976d2;
                animation: pulse 1.5s infinite;
            }

            .verification-status.requires_captcha {
                background: #fff3e0;
                color: #f57c00;
            }

            .verification-status.requires_2fa {
                background: #e8f5e9;
                color: #388e3c;
            }

            .verification-status.suspended {
                background: #ffebee;
                color: #d32f2f;
            }

            .verification-status.rate_limited {
                background: #fce4ec;
                color: #c2185b;
            }

            .verification-status.server_error,
            .verification-status.network_error,
            .verification-status.timeout,
            .verification-status.error {
                background: #f3e5f5;
                color: #7b1fa2;
            }

            .progress-info {
                margin: 10px 0;
                padding: 10px;
                background: #f5f5f5;
                border-radius: 6px;
                font-size: 13px;
                color: #666;
            }

            .batch-actions {
                margin-bottom: 15px;
                padding: 10px;
                background: #f9f9f9;
                border-radius: 6px;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .select-all-checkbox {
                margin-right: 8px;
            }

            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: none;
            }

            .modal-overlay.show {
                display: block;
            }

            /* 分页样式 */
            .pagination-container {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 15px 0;
                gap: 10px;
            }

            .pagination-btn {
                padding: 6px 12px;
                border: 1px solid #ddd;
                background: white;
                color: #333;
                cursor: pointer;
                border-radius: 4px;
                font-size: 12px;
                transition: all 0.2s;
            }

            .pagination-btn:hover {
                background: #f5f5f5;
            }

            .pagination-btn:disabled {
                background: #f9f9f9;
                color: #ccc;
                cursor: not-allowed;
            }

            .pagination-btn.active {
                background: #667eea;
                color: white;
                border-color: #667eea;
            }

            .pagination-info {
                font-size: 12px;
                color: #666;
                margin: 0 10px;
            }
        `);

        panel.innerHTML = `
            <div class="panel-header">
                <div class="panel-title">
                    <span>🤖</span>
                    <span>GitHub 自动注册</span>
                    <div class="minimized-status" id="minimized-status-text">准备就绪</div>
                </div>
                <div class="panel-controls">
                    <button class="panel-btn library-btn" title="当前账号库">📚</button>
                    <button class="panel-btn history-btn" title="历史账号库">📋</button>
                    <button class="panel-btn minimize-btn" title="最小化">_</button>
                    <button class="panel-btn close-btn">×</button>
                </div>
            </div>
            <div class="panel-body">
                <div class="status-container"></div>
                <div class="info-section" style="display: none;">
                    <div class="info-item">
                        <span class="info-label">邮箱:</span>
                        <span class="info-value email-value">-</span>
                        <button class="copy-btn" data-copy="email">复制</button>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用户名:</span>
                        <span class="info-value username-value">-</span>
                        <button class="copy-btn" data-copy="username">复制</button>
                    </div>
                    <div class="info-item">
                        <span class="info-label">密码:</span>
                        <span class="info-value password-value">***</span>
                        <button class="copy-btn" data-copy="password">复制</button>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="action-btn primary start-btn">开始注册</button>
                    <button class="action-btn secondary reset-btn">重置状态</button>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // 添加账号库弹窗
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'modal-overlay';

        const modal = document.createElement('div');
        modal.className = 'account-library-modal';
        modal.innerHTML = `
            <div class="modal-header">
                <h3 style="margin: 0; font-size: 16px;">📚 账号库管理</h3>
                <button class="panel-btn close-modal-x-btn">×</button>
            </div>
            <div class="modal-tabs">
                <button class="modal-tab active" data-tab="current">当前账号库</button>
                <button class="modal-tab" data-tab="history">历史账号库</button>
            </div>
            <div class="modal-body">
                <!-- 当前账号库标签页 -->
                <div class="tab-content active" id="current-tab">
                    <div class="batch-actions">
                        <button class="action-btn secondary" id="clear-current-btn">🧹 清空账号库</button>
                        <span class="account-count">共 <span id="current-account-total">0</span> 个账号</span>
                    </div>
                    <div class="progress-info" id="current-progress" style="display: none;"></div>
                    <table class="account-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>邮箱</th>
                                <th></th>
                                <th>用户名</th>
                                <th>密码</th>
                                <th></th>
                                <th>创建时间</th>
                                <th>注册状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="current-account-list">
                            <!-- 当前账号列表将在这里动态生成 -->
                        </tbody>
                    </table>
                    <div class="pagination-container" id="current-pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </div>
                </div>

                <!-- 历史账号库标签页 -->
                <div class="tab-content" id="history-tab">
                    <div class="batch-actions">
                        <input type="checkbox" class="select-all-checkbox" id="history-select-all">
                        <label for="history-select-all">全选</label>
                        <button class="action-btn secondary" id="batch-delete-history-btn">🗑️ 批量删除</button>
                        <button class="action-btn secondary" id="clear-history-btn">🧹 清空历史</button>
                        <span class="account-count">共 <span id="history-account-total">0</span> 个账号</span>
                    </div>
                    <div class="progress-info" id="history-progress" style="display: none;"></div>
                    <table class="account-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="history-header-checkbox"></th>
                                <th>序号</th>
                                <th>邮箱</th>
                                <th></th>
                                <th>密码</th>
                                <th></th>
                                <th>导出时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="history-account-list">
                            <!-- 历史账号列表将在这里动态生成 -->
                        </tbody>
                    </table>
                    <div class="pagination-container" id="history-pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-buttons">
                    <button class="export-accounts-btn">📤 导出所有账号</button>
                    <button class="close-modal-btn">关闭</button>
                </div>
            </div>
        `;

        document.body.appendChild(modalOverlay);
        document.body.appendChild(modal);

        // 添加拖拽功能
        makeDraggable(panel);

        // 绑定事件
        panel.querySelector('.minimize-btn').addEventListener('click', () => {
            const isMinimized = panel.classList.contains('minimized');
            panel.classList.toggle('minimized');

            // 更新最小化按钮文本
            const minimizeBtn = panel.querySelector('.minimize-btn');
            if (panel.classList.contains('minimized')) {
                minimizeBtn.textContent = '□';
                minimizeBtn.title = '展开';
                // 在缩小时显示最新状态
                updateMinimizedStatus();
            } else {
                minimizeBtn.textContent = '_';
                minimizeBtn.title = '最小化';
            }
        });

        panel.querySelector('.close-btn').addEventListener('click', () => {
            panel.remove();
        });

        // 当前账号库按钮
        panel.querySelector('.library-btn').addEventListener('click', () => {
            showAccountLibrary('current');
        });

        // 历史账号库按钮
        panel.querySelector('.history-btn').addEventListener('click', () => {
            showAccountLibrary('history');
        });

        // 关闭弹窗
        modalOverlay.addEventListener('click', () => {
            modal.classList.remove('show');
            modalOverlay.classList.remove('show');
        });

        modal.querySelector('.close-modal-btn').addEventListener('click', () => {
            modal.classList.remove('show');
            modalOverlay.classList.remove('show');
        });

        // 绑定右上角X按钮关闭事件
        modal.querySelector('.close-modal-x-btn').addEventListener('click', () => {
            modal.classList.remove('show');
            modalOverlay.classList.remove('show');
        });

        // 绑定导出按钮事件
        modal.querySelector('.export-accounts-btn').addEventListener('click', () => {
            exportAllAccounts();
        });

        // 绑定复制按钮事件 - 修改为使用随机密码
        panel.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const type = btn.getAttribute('data-copy');
                let value = '';

                switch(type) {
                    case 'email':
                        value = GM_getValue('github_email', '');
                        break;
                    case 'username':
                        value = GM_getValue('github_username', '');
                        break;
                    case 'password':
                        value = GM_getValue('github_password', '');
                        break;
                }

                if (value) {
                    GM_setClipboard(value);
                    const originalText = btn.textContent;
                    btn.textContent = '已复制!';
                    btn.style.background = '#4CAF50';
                    btn.style.color = 'white';

                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.background = '#e0e0e0';
                        btn.style.color = '';
                    }, 1500);
                }
            });
        });

        return panel;
    }

    // 显示账号库
    function showAccountLibrary(activeTab = 'current') {
        const modal = document.querySelector('.account-library-modal');
        const overlay = document.querySelector('.modal-overlay');

        // 初始化标签页
        initializeTabs(modal, activeTab);

        // 显示当前账号库
        showCurrentAccountTab();

        // 显示历史账号库
        showHistoryAccountTab();

        modal.classList.add('show');
        overlay.classList.add('show');
    }

    // 初始化标签页
    function initializeTabs(modal, activeTab) {
        const tabs = modal.querySelectorAll('.modal-tab');
        const tabContents = modal.querySelectorAll('.tab-content');

        // 绑定标签页切换事件
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.getAttribute('data-tab');

                // 更新标签页状态
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(tc => tc.classList.remove('active'));

                tab.classList.add('active');
                document.getElementById(`${tabName}-tab`).classList.add('active');

                // 刷新对应的内容
                if (tabName === 'current') {
                    showCurrentAccountTab();
                } else if (tabName === 'history') {
                    showHistoryAccountTab();
                }
            });
        });

        // 设置默认激活标签页
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(tc => tc.classList.remove('active'));

        const activeTabElement = modal.querySelector(`[data-tab="${activeTab}"]`);
        const activeContentElement = document.getElementById(`${activeTab}-tab`);

        if (activeTabElement && activeContentElement) {
            activeTabElement.classList.add('active');
            activeContentElement.classList.add('active');
        }
    }

    // 显示当前账号库标签页
    function showCurrentAccountTab(page = 1) {
        const library = getAccountLibrary();
        const tbody = document.getElementById('current-account-list');
        const total = document.getElementById('current-account-total');
        const exportBtn = document.querySelector('.export-accounts-btn');
        
        const pageSize = 5; // 每页显示5个账号
        const totalPages = Math.ceil(library.length / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const currentPageData = library.slice(startIndex, endIndex);

        total.textContent = library.length;

        // 更新导出按钮状态
        if (exportBtn) {
            exportBtn.disabled = library.length === 0;
            exportBtn.textContent = `📤 导出所有账号 (${library.length})`;
        }

        // 清空列表
        tbody.innerHTML = '';

        // 生成当前页账号列表
        currentPageData.forEach((account, index) => {
            const globalIndex = startIndex + index + 1; // 全局序号
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${globalIndex}</td>
                <td>${account.email}</td>
                <td><button class="copy-btn" data-email="${account.email.replace(/"/g, '&quot;')}" style="font-size: 11px;">复制</button></td>
                <td>${account.username || '-'}</td>
                <td><span style="font-family: monospace;">${account.password}</span></td>
                <td><button class="copy-btn" data-password="${account.password.replace(/"/g, '&quot;')}" style="font-size: 11px;">复制</button></td>
                <td>${account.createdAt}</td>
                <td>
                    <span class="account-status ${account.registrationSuccess ? 'success' : 'pending'}">
                        ${account.registrationSuccess ? '成功' : '待验证'}
                    </span>
                </td>
                <td>
                    <button class="delete-account-btn" data-id="${account.id}">删除</button>
                </td>
            `;
            tbody.appendChild(tr);
        });

        // 绑定删除按钮事件
        tbody.querySelectorAll('.delete-account-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const id = btn.getAttribute('data-id');
                if (confirm('确定要删除这个账号吗？')) {
                    deleteAccountFromLibrary(id);
                    // 删除后可能需要调整页码
                    const newLibrary = getAccountLibrary();
                    const newTotalPages = Math.ceil(newLibrary.length / pageSize);
                    const adjustedPage = Math.min(page, Math.max(1, newTotalPages));
                    showCurrentAccountTab(adjustedPage); // 刷新列表
                }
            });
        });

        // 绑定复制按钮事件
        tbody.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                let value = '';
                if (btn.hasAttribute('data-email')) {
                    value = btn.getAttribute('data-email');
                } else if (btn.hasAttribute('data-password')) {
                    value = btn.getAttribute('data-password');
                }
                
                if (value) {
                    GM_setClipboard(value);
                    const originalText = btn.textContent;
                    btn.textContent = '已复制!';
                    btn.style.background = '#4CAF50';
                    btn.style.color = 'white';

                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.background = '';
                        btn.style.color = '';
                    }, 1500);
                }
            });
        });

        // 创建分页控件
        createPagination(library.length, page, pageSize, 'current-pagination', (newPage) => {
            showCurrentAccountTab(newPage);
        });

        // 绑定清空当前账号库按钮事件
        const clearCurrentBtn = document.getElementById('clear-current-btn');
        if (clearCurrentBtn && !clearCurrentBtn._bound) {
            clearCurrentBtn._bound = true; // 防止重复绑定
            clearCurrentBtn.onclick = () => {
                if (clearCurrentAccountLibrary()) {
                    showCurrentAccountTab(1); // 刷新到第一页
                }
            };
        }
    }

    // 显示历史账号库标签页
    function showHistoryAccountTab(page = 1) {
        const historyLibrary = getHistoryAccountLibrary();
        const tbody = document.getElementById('history-account-list');
        const total = document.getElementById('history-account-total');
        const batchDeleteBtn = document.getElementById('batch-delete-history-btn');
        const clearHistoryBtn = document.getElementById('clear-history-btn');
        const selectAllCheckbox = document.getElementById('history-select-all');
        const headerCheckbox = document.getElementById('history-header-checkbox');

        const pageSize = 5; // 每页显示5个账号
        const totalPages = Math.ceil(historyLibrary.length / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const currentPageData = historyLibrary.slice(startIndex, endIndex);

        total.textContent = historyLibrary.length;

        // 清空列表
        tbody.innerHTML = '';

        // 生成历史账号列表
        currentPageData.forEach((account, index) => {
            const globalIndex = startIndex + index + 1; // 全局序号
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td><input type="checkbox" class="account-checkbox" data-id="${account.id}"></td>
                <td>${globalIndex}</td>
                <td>${account.email}</td>
                <td><button class="copy-btn" data-email="${account.email.replace(/"/g, '&quot;')}" style="font-size: 11px;">复制</button></td>
                <td><span style="font-family: monospace;">${account.password}</span></td>
                <td><button class="copy-btn" data-password="${account.password.replace(/"/g, '&quot;')}" style="font-size: 11px;">复制</button></td>
                <td>${account.exportedAt}</td>
                <td>
                    <button class="delete-account-btn" data-id="${account.id}">删除</button>
                </td>
            `;
            tbody.appendChild(tr);
        });

        // 创建分页控件
        createPagination(historyLibrary.length, page, pageSize, 'history-pagination', (newPage) => {
            showHistoryAccountTab(newPage);
        });

        // 绑定事件
        bindHistoryAccountEvents(tbody, batchDeleteBtn, clearHistoryBtn, selectAllCheckbox, headerCheckbox, historyLibrary, page);
    }

    // 获取验证状态文本
    function getVerificationStatusText(status) {
        const statusMap = {
            'pending': '待验证',
            'valid': '有效',
            'invalid': '失效',
            'verifying': '验证中',
            'requires_captcha': '需要验证码',
            'requires_2fa': '需要2FA',
            'suspended': '账号暂停',
            'rate_limited': '请求限制',
            'server_error': '服务器错误',
            'network_error': '网络错误',
            'timeout': '请求超时',
            'error': '验证出错'
        };
        return statusMap[status] || '未知';
    }

    // 绑定历史账号库事件
    function bindHistoryAccountEvents(tbody, batchDeleteBtn, clearHistoryBtn, selectAllCheckbox, headerCheckbox, historyLibrary, page = 1) {
        const pageSize = 5;
        
        // 绑定删除按钮事件
        tbody.querySelectorAll('.delete-account-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const id = btn.getAttribute('data-id');
                if (confirm('确定要删除这个历史账号吗？')) {
                    deleteFromHistoryLibrary(id);
                    // 删除后可能需要调整页码
                    const newLibrary = getHistoryAccountLibrary();
                    const newTotalPages = Math.ceil(newLibrary.length / pageSize);
                    const adjustedPage = Math.min(page, Math.max(1, newTotalPages));
                    showHistoryAccountTab(adjustedPage); // 刷新列表
                }
            });
        });

        // 绑定复制按钮事件
        tbody.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                let value = '';
                if (btn.hasAttribute('data-email')) {
                    value = btn.getAttribute('data-email');
                } else if (btn.hasAttribute('data-password')) {
                    value = btn.getAttribute('data-password');
                }
                
                if (value) {
                    GM_setClipboard(value);
                    const originalText = btn.textContent;
                    btn.textContent = '已复制!';
                    btn.style.background = '#4CAF50';
                    btn.style.color = 'white';

                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.background = '';
                        btn.style.color = '';
                    }, 1500);
                }
            });
        });

        // 全选功能
        const updateSelectAllState = () => {
            const checkboxes = tbody.querySelectorAll('.account-checkbox');
            const checkedCount = tbody.querySelectorAll('.account-checkbox:checked').length;

            selectAllCheckbox.checked = checkedCount === checkboxes.length && checkboxes.length > 0;
            headerCheckbox.checked = selectAllCheckbox.checked;

            batchDeleteBtn.disabled = checkedCount === 0;
        };

        selectAllCheckbox.addEventListener('change', () => {
            const checkboxes = tbody.querySelectorAll('.account-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
            headerCheckbox.checked = selectAllCheckbox.checked;
            updateSelectAllState();
        });

        headerCheckbox.addEventListener('change', () => {
            selectAllCheckbox.checked = headerCheckbox.checked;
            selectAllCheckbox.dispatchEvent(new Event('change'));
        });

        tbody.querySelectorAll('.account-checkbox').forEach(cb => {
            cb.addEventListener('change', updateSelectAllState);
        });

        // 批量删除
        if (batchDeleteBtn && !batchDeleteBtn._bound) {
            batchDeleteBtn._bound = true;
            batchDeleteBtn.addEventListener('click', () => {
                const checkedIds = Array.from(tbody.querySelectorAll('.account-checkbox:checked'))
                    .map(cb => cb.getAttribute('data-id'));

                if (checkedIds.length === 0) {
                    alert('请选择要删除的账号！');
                    return;
                }

                if (confirm(`确定要删除选中的 ${checkedIds.length} 个历史账号吗？`)) {
                    batchDeleteFromHistoryLibrary(checkedIds);
                    // 删除后可能需要调整页码
                    const newLibrary = getHistoryAccountLibrary();
                    const newTotalPages = Math.ceil(newLibrary.length / pageSize);
                    const adjustedPage = Math.min(page, Math.max(1, newTotalPages));
                    showHistoryAccountTab(adjustedPage);
                }
            });
        }

        // 清空历史
        if (clearHistoryBtn && !clearHistoryBtn._bound) {
            clearHistoryBtn._bound = true;
            clearHistoryBtn.addEventListener('click', () => {
                if (historyLibrary.length === 0) {
                    alert('历史账号库已经是空的！');
                    return;
                }

                if (confirm(`确定要清空所有 ${historyLibrary.length} 个历史账号吗？此操作不可恢复！`)) {
                    clearHistoryLibrary();
                    showHistoryAccountTab(1); // 回到第一页
                }
            });
        }

        // 初始化状态
        updateSelectAllState();
    }

    // 使元素可拖拽
    function makeDraggable(element) {
        const header = element.querySelector('.panel-header');
        let isDragging = false;
        let startX, startY, initialX, initialY;

        header.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('panel-btn')) return;

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            const rect = element.getBoundingClientRect();
            initialX = rect.left;
            initialY = rect.top;

            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', handleDragEnd);
        });

        function handleDrag(e) {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            element.style.left = `${initialX + deltaX}px`;
            element.style.top = `${initialY + deltaY}px`;
            element.style.right = 'auto';
            element.style.bottom = 'auto';
        }

        function handleDragEnd() {
            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', handleDragEnd);
        }
    }

    // 更新缩小状态显示
    function updateMinimizedStatus() {
        const minimizedStatusElement = document.getElementById('minimized-status-text');
        if (!minimizedStatusElement) return;

        const state = GM_getValue('registration_state', 'initial');
        const email = GM_getValue('github_email', '');

        let statusText = '';

        switch(state) {
            case 'initial':
                statusText = '准备就绪';
                break;
            case 'github_signup':
                statusText = '正在注册...';
                break;
            case 'github_verify':
                statusText = '等待验证码';
                break;
            case 'github_login_after_verify':
                statusText = '验证完成';
                break;
            case 'complete':
                statusText = '注册完成 ✓';
                break;
            default:
                statusText = '运行中...';
        }

        if (email && state !== 'initial') {
            const shortEmail = email.length > 20 ? email.substring(0, 17) + '...' : email;
            statusText += ` | ${shortEmail}`;
        }

        minimizedStatusElement.textContent = statusText;
    }

    // 更新状态
    function updateStatus(message, type = 'processing') {
        const panel = document.getElementById('github-auto-register-panel');
        if (!panel) return;

        const statusContainer = panel.querySelector('.status-container');
        const statusItem = document.createElement('div');
        statusItem.className = 'status-item';

        const iconMap = {
            'success': '✓',
            'error': '✗',
            'pending': '⋯',
            'processing': '◷'
        };

        const time = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        statusItem.innerHTML = `
            <div class="status-icon ${type}">${iconMap[type]}</div>
            <div class="status-text">${message}</div>
            <div class="status-time">${time}</div>
        `;

        statusContainer.appendChild(statusItem);
        statusContainer.scrollTop = statusContainer.scrollHeight;

        // 保持最多20条状态记录
        const items = statusContainer.querySelectorAll('.status-item');
        if (items.length > 20) {
            items[0].remove();
        }

        // 更新缩小状态显示
        updateMinimizedStatus();
    }

    // 更新账号信息 - 修改为使用随机密码
    function updateAccountInfo(email, username, showPassword = false) {
        const panel = document.getElementById('github-auto-register-panel');
        if (!panel) return;

        const infoSection = panel.querySelector('.info-section');
        infoSection.style.display = 'block';

        if (email) {
            panel.querySelector('.email-value').textContent = email;
        }
        if (username) {
            panel.querySelector('.username-value').textContent = username;
        }
        if (showPassword) {
            panel.querySelector('.password-value').textContent = GM_getValue('github_password', '***');
        }

        // 更新缩小状态显示
        updateMinimizedStatus();
    }

    // ======================== TempMail API ========================

    // 获取邮件列表
    async function getMailList() {
        const url = `${CONFIG.TEMPMAIL.API_BASE}/mails?email=${CONFIG.TEMPMAIL.USERNAME}${CONFIG.TEMPMAIL.DOMAIN}&limit=20&epin=${CONFIG.TEMPMAIL.PIN}`;

        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                onload: (response) => {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data);
                    } catch (error) {
                        reject(error);
                    }
                },
                onerror: (error) => {
                    reject(error);
                }
            });
        });
    }

    // 获取邮件详情
    async function getMailDetail(mailId) {
        const url = `${CONFIG.TEMPMAIL.API_BASE}/mails/${mailId}?email=${CONFIG.TEMPMAIL.USERNAME}${CONFIG.TEMPMAIL.DOMAIN}&epin=${CONFIG.TEMPMAIL.PIN}`;

        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'GET',
                url: url,
                onload: (response) => {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data);
                    } catch (error) {
                        reject(error);
                    }
                },
                onerror: (error) => {
                    reject(error);
                }
            });
        });
    }

    // 删除邮件
    async function deleteMail(mailId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'DELETE',
                url: `${CONFIG.TEMPMAIL.API_BASE}/mails/`,
                data: `email=${CONFIG.TEMPMAIL.USERNAME}${CONFIG.TEMPMAIL.DOMAIN}&first_id=${mailId}&epin=${CONFIG.TEMPMAIL.PIN}`,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                onload: (response) => {
                    try {
                        const data = JSON.parse(response.responseText);
                        resolve(data.result === true);
                    } catch (error) {
                        resolve(false);
                    }
                },
                onerror: () => {
                    resolve(false);
                }
            });
        });
    }

    // 提取验证码
    function extractVerificationCode(text) {
        // GitHub验证码通常是6-8位数字
        const patterns = [
            /\b\d{6}\b/,        // 6位数字
            /\b\d{7}\b/,        // 7位数字
            /\b\d{8}\b/,        // 8位数字
            /\b\d{4}\s*\d{4}\b/ // 4+4格式
        ];

        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match) {
                return match[0].replace(/\s/g, '');
            }
        }

        return null;
    }

    // 等待并获取验证码 - 带详细进度提示
    async function waitForVerificationCode(maxRetries = CONFIG.RETRY.MAX_ATTEMPTS) {
        updateStatus('🚀 开始自动获取验证码...', 'processing');
        log('开始验证码获取流程', 'info');

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            const attemptNumber = attempt + 1;
            log(`尝试获取验证码 (${attemptNumber}/${maxRetries})`, 'debug');
            updateStatus(`🔄 尝试获取验证码 (第 ${attemptNumber}/${maxRetries} 次)...`, 'processing');

            try {
                updateStatus(`📧 正在检查邮箱...`, 'processing');
                const mailList = await getMailList();

                if (mailList.result && mailList.first_id) {
                    updateStatus('✉️ 发现新邮件，正在读取...', 'processing');
                    log(`发现邮件ID: ${mailList.first_id}`, 'debug');

                    // 获取最新邮件详情
                    const mailDetail = await getMailDetail(mailList.first_id);

                    if (mailDetail.result) {
                        const subject = mailDetail.subject || '';
                        const text = mailDetail.text || '';

                        log(`邮件主题: ${subject}`, 'debug');

                // 检查是否是GitHub的邮件 - 支持中英文
                        if (subject.toLowerCase().includes('github') ||
                            subject.includes('GitHub') ||
                            subject.includes('验证') ||
                            subject.includes('verification') ||
                            subject.includes('Verification') ||
                            subject.includes('验证码') ||
                            subject.includes('code') ||
                            text.toLowerCase().includes('github') ||
                            text.includes('GitHub') ||
                            text.includes('验证') ||
                            text.includes('verification')) {

                            updateStatus(`✅ 找到GitHub邮件: ${subject}`, 'success');
                            log(`GitHub邮件内容预览: ${text.substring(0, 100)}...`, 'debug');

                            const code = extractVerificationCode(text);
                            if (code) {
                                updateStatus(`🎉 成功获取验证码: ${code}`, 'success');
                                log(`验证码提取成功: ${code}`, 'success');

                                // 删除已读邮件
                                updateStatus('🗑️ 正在删除已读邮件...', 'processing');
                                await deleteMail(mailList.first_id);

                                return code;
                            } else {
                                updateStatus('⚠️ 邮件中未找到验证码', 'warning');
                                log('无法从邮件中提取验证码', 'warning');
                            }
                        } else {
                            updateStatus('❌ 邮件不是GitHub验证邮件', 'error');
                            log(`非GitHub邮件，主题: ${subject}`, 'warning');
                        }
                    } else {
                        updateStatus('❌ 无法读取邮件详情', 'error');
                    }
                } else {
                    updateStatus(`⏳ 暂无新邮件，等待中...`, 'pending');
                    log('邮箱中暂无新邮件', 'debug');
                }
            } catch (error) {
                log(`获取邮件出错: ${error}`, 'error');
                updateStatus(`❌ 获取邮件出错: ${error.message}`, 'error');
            }

            if (attempt < maxRetries - 1) {
                const waitSeconds = CONFIG.RETRY.EMAIL_CHECK_INTERVAL / 1000;
                updateStatus(`⏰ ${waitSeconds}秒后重试...`, 'pending');
                log(`等待${waitSeconds}秒后重试`, 'debug');
                await sleep(CONFIG.RETRY.EMAIL_CHECK_INTERVAL);
            }
        }

        updateStatus(`❌ 经过 ${maxRetries} 次尝试后仍未获取到验证码`, 'error');
        log(`验证码获取失败，已尝试${maxRetries}次`, 'error');
        throw new Error('未能获取到验证码');
    }

    // ======================== 页面处理函数 ========================

    // 处理AnyRouter注册页面
    async function handleAnyRouterRegisterPage() {
        log('处理AnyRouter注册页面');
        updateStatus('准备点击GitHub登录按钮', 'processing');

        // 等待页面加载完成
        await sleep(2000);

        // 尝试多种选择器查找GitHub登录按钮
        const selectors = [
            'button.semi-button-with-icon',
            'button[class*="semi-button"]',
            'button:has(svg[class*="github"])',
            'button:has(span[class*="github"])'
        ];

        let githubButton = null;

        // 尝试不同的选择器
        for (const selector of selectors) {
            try {
                const buttons = document.querySelectorAll(selector);
                for (const button of buttons) {
                    const buttonText = button.textContent || button.innerText || '';
                    log(`检查按钮: "${buttonText}"`, 'debug');

                    // 检查中文或英文文本
                    if (buttonText.includes('GitHub') ||
                        buttonText.includes('github') ||
                        buttonText.includes('使用 GitHub') ||
                        buttonText.includes('Continue with GitHub')) {
                        githubButton = button;
                        break;
                    }
                }
                if (githubButton) break;
            } catch (e) {
                continue;
            }
        }

        // 如果还没找到，查找包含GitHub图标的按钮
        if (!githubButton) {
            const allButtons = document.querySelectorAll('button');
            for (const button of allButtons) {
                // 检查是否有GitHub相关的类名或图标
                if (button.innerHTML.includes('github') ||
                    button.className.includes('github')) {
                    githubButton = button;
                    break;
                }
            }
        }

        if (githubButton) {
            log(`找到GitHub按钮: "${githubButton.textContent}"`, 'success');
            updateStatus('点击GitHub登录按钮', 'processing');

            // 确保按钮可见
            githubButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(500);

            // 尝试多种点击方式
            try {
                // 方式1：直接点击
                githubButton.click();
            } catch (e) {
                // 方式2：模拟点击
                await clickElement(githubButton);
            }

            // 保存注册状态
            GM_setValue('registration_state', 'github_login');

            // 等待页面跳转
            await sleep(2000);
            return true;
        }

        log('未找到GitHub登录按钮', 'error');
        updateStatus('未找到GitHub登录按钮，请检查页面', 'error');
        return false;
    }

    // 处理GitHub登录页面
    async function handleGitHubLoginPage() {
        log('处理GitHub登录页面');

        // 等待页面完全加载
        await sleep(2000);

        // 检查是否需要登录
        const loginForm = document.querySelector('form[action="/session"]');
        if (loginForm) {
            updateStatus('检测到需要GitHub登录', 'processing');

            // 查找创建账号的链接 - 支持中文和英文
            let createAccountLink = null;

            // 查找所有链接 - 支持中英文
            const links = document.querySelectorAll('a');
            for (const link of links) {
                const linkText = link.textContent || link.innerText || '';
                log(`检查链接: "${linkText}"`, 'debug');

                if (linkText.includes('那就注册个账户吧') ||
                    linkText.includes('Create an account') ||
                    linkText.includes('创建账户') ||
                    linkText.includes('创建账号') ||
                    linkText.includes('注册账户') ||
                    linkText.includes('注册账号') ||
                    linkText.includes('Sign up') ||
                    linkText.includes('注册') ||
                    link.href.includes('join') ||
                    link.href.includes('signup')) {
                    createAccountLink = link;
                    break;
                }
            }

            if (createAccountLink) {
                log(`找到创建账号链接: "${createAccountLink.textContent}"`, 'success');
                updateStatus('点击创建GitHub账号', 'processing');

                // 确保链接可见
                createAccountLink.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await sleep(500);

                // 点击链接
                try {
                    createAccountLink.click();
                } catch (e) {
                    await clickElement(createAccountLink);
                }

                GM_setValue('registration_state', 'github_signup');
                await sleep(2000);
                return true;
            } else {
                log('未找到创建账号链接', 'error');
                updateStatus('未找到创建账号链接', 'error');
            }
        }

        return false;
    }

    // ======================== GitHub注册页面处理 ========================

    // 处理GitHub注册页面 - 修改为使用随机密码
    async function handleGitHubSignupPage() {
        log('处理GitHub注册页面');

        // 检查是否在注册页面 - 支持多种URL格式
        if (!window.location.href.includes('/join') &&
            !window.location.href.includes('/signup')) {
            return false;
        }

        updateStatus('开始填写GitHub注册信息', 'processing');

        // 等待页面加载
        await sleep(2000);

        // 生成账号信息 - 使用随机密码
        const email = generateEmail();
        const username = generateUsername();
        const password = generatePassword(); // 使用随机密码生成函数

        // 保存账号信息到库
        const accountId = saveAccountToLibrary(email, username, password);
        GM_setValue('current_account_id', accountId);

        // 保存账号信息到GM存储
        GM_setValue('github_email', email);
        GM_setValue('github_username', username);
        GM_setValue('github_password', password); // 保存随机密码

        updateAccountInfo(email, username, true); // 显示密码

        // 查找并填写邮箱 - 使用更灵活的选择器，支持中英文
        const emailSelectors = [
            'input[type="email"]',
            'input[name*="email"]',
            'input[id*="email"]',
            'input[placeholder*="邮件"]',
            'input[placeholder*="email"]',
            'input[placeholder*="Email"]',
            'input[placeholder*="邮箱"]',
            'input[autocomplete="email"]'
        ];

        let emailInput = null;
        for (const selector of emailSelectors) {
            emailInput = await waitForElement(selector, 3000);
            if (emailInput) break;
        }

        if (emailInput) {
            directFillInput(emailInput, email);
            updateStatus(`填写邮箱: ${email}`, 'success');
        } else {
            log('未找到邮箱输入框', 'error');
        }

        // 查找并填写密码 - 支持中英文
        const passwordSelectors = [
            'input[type="password"]',
            'input[name*="password"]',
            'input[id*="password"]',
            'input[placeholder*="密码"]',
            'input[placeholder*="password"]',
            'input[placeholder*="Password"]',
            'input[autocomplete="new-password"]',
            'input[autocomplete="current-password"]'
        ];

        let passwordInput = null;
        for (const selector of passwordSelectors) {
            passwordInput = await waitForElement(selector, 3000);
            if (passwordInput) break;
        }

        if (passwordInput) {
            directFillInput(passwordInput, password);
            updateStatus('填写随机密码', 'success');
        } else {
            log('未找到密码输入框', 'error');
        }

        // 查找并填写用户名 - 支持中英文
        const usernameSelectors = [
            'input[name*="login"]',
            'input[name*="username"]',
            'input[id*="login"]',
            'input[id*="username"]',
            'input[placeholder*="用户名"]',
            'input[placeholder*="username"]',
            'input[placeholder*="Username"]',
            'input[placeholder*="用户"]',
            'input[autocomplete="username"]:not([type="email"])',
            'input[autocomplete="nickname"]'
        ];

        let usernameInput = null;
        for (const selector of usernameSelectors) {
            const inputs = document.querySelectorAll(selector);
            for (const input of inputs) {
                // 排除邮箱和密码输入框
                if (input.type !== 'email' && input.type !== 'password') {
                    usernameInput = input;
                    break;
                }
            }
            if (usernameInput) break;
        }

        if (usernameInput) {
            directFillInput(usernameInput, username);
            updateStatus(`填写用户名: ${username}`, 'success');
        } else {
            log('未找到用户名输入框', 'error');
        }

        // 处理营销邮件选项 - 支持中英文
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        for (const checkbox of checkboxes) {
            // 查找 Email preferences 相关的复选框
            const label = checkbox.closest('label') ||
                         document.querySelector(`label[for="${checkbox.id}"]`);
            if (label) {
                const labelText = label.textContent || label.innerText || '';
                if (labelText.includes('Email') ||
                    labelText.includes('邮件') ||
                    labelText.includes('product updates') ||
                    labelText.includes('产品更新') ||
                    labelText.includes('occasional') ||
                    labelText.includes('偶尔') ||
                    labelText.includes('newsletter') ||
                    labelText.includes('通讯') ||
                    labelText.includes('marketing') ||
                    labelText.includes('营销')) {
                    if (!checkbox.checked) {
                        await clickElement(checkbox);
                        updateStatus('勾选邮件选项', 'success');
                    }
                    break;
                }
            }
        }

        // 等待并点击继续按钮
        await sleep(500); // 确保表单填写完成

        // 查找继续按钮 - 使用多种方式
        let continueButton = null;

        // 方式1: 查找包含"Create account"文本的按钮 - 支持中英文
        const buttons = document.querySelectorAll('button');
        for (const button of buttons) {
            const buttonText = button.textContent || button.innerText || '';
            log(`检查按钮文本: "${buttonText.trim()}"`, 'debug');
            if (buttonText.includes('Create account') ||
                buttonText.includes('创建账户') ||
                buttonText.includes('创建账号') ||
                buttonText.includes('注册账户') ||
                buttonText.includes('注册账号') ||
                buttonText.includes('Sign up') ||
                buttonText.includes('Continue') ||
                buttonText.includes('继续') ||
                buttonText.includes('提交') ||
                buttonText.includes('Submit')) {
                continueButton = button;
                log(`找到创建账户按钮: "${buttonText.trim()}"`, 'success');
                break;
            }
        }

        // 方式2: 通过按钮的data属性查找
        if (!continueButton) {
            continueButton = document.querySelector('button[data-continue-to="verification-sent"]');
            if (continueButton) {
                log('通过data属性找到按钮', 'success');
            }
        }

        // 方式3: 查找 type="submit" 的按钮
        if (!continueButton) {
            continueButton = await waitForElement('button[type="submit"]', 2000);
            if (continueButton) {
                log('通过type="submit"找到按钮', 'success');
            }
        }

        // 方式4: 查找有特定类名的按钮
        if (!continueButton) {
            const selectors = [
                'button.btn-primary',
                'button[class*="primary"]',
                'button[class*="signup"]',
                'input[type="submit"]'
            ];

            for (const selector of selectors) {
                continueButton = document.querySelector(selector);
                if (continueButton && continueButton.offsetParent !== null) {
                    log(`通过选择器找到按钮: ${selector}`, 'success');
                    break;
                }
            }
        }

        // 方式5: 查找表单中的提交按钮
        if (!continueButton) {
            const form = document.querySelector('form');
            if (form) {
                const formButtons = form.querySelectorAll('button');
                for (const btn of formButtons) {
                    if (btn.type === 'submit' || !btn.type) {
                        continueButton = btn;
                        log('通过表单找到提交按钮', 'success');
                        break;
                    }
                }
            }
        }

        if (continueButton) {
            updateStatus('✅ 找到创建账户按钮，准备点击...', 'success');
            log(`按钮元素信息: tagName="${continueButton.tagName}", type="${continueButton.type}", className="${continueButton.className}", disabled="${continueButton.disabled}"`, 'debug');

            // 确保按钮可见和可点击
            if (continueButton.disabled) {
                updateStatus('⚠️ 按钮被禁用，等待启用...', 'warning');
                // 等待按钮启用
                let waitCount = 0;
                while (continueButton.disabled && waitCount < 20) {
                    await sleep(500);
                    waitCount++;
                    updateStatus(`等待按钮启用... (${waitCount}/20)`, 'processing');
                }

                if (continueButton.disabled) {
                    updateStatus('❌ 按钮仍然被禁用', 'error');
                    return false;
                }
            }

            // 滚动到按钮位置
            continueButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await sleep(1000);

            updateStatus('🚀 点击创建账户按钮', 'processing');

            try {
                // 尝试多种点击方式
                const clickSuccess = await tryMultipleClicks(continueButton);

                if (clickSuccess) {
                    GM_setValue('registration_state', 'github_verify');
                    updateStatus('✅ 成功点击按钮，等待跳转...', 'success');

                    // 等待页面跳转或变化
                    await sleep(3000);
                    return true;
                } else {
                    updateStatus('❌ 所有点击方式都失败了', 'error');
                    return false;
                }
            } catch (error) {
                log(`点击按钮时出错: ${error}`, 'error');
                updateStatus(`点击按钮出错: ${error.message}`, 'error');
                return false;
            }
        } else {
            log('未找到创建账户按钮', 'error');
            updateStatus('❌ 未找到创建账户按钮，请手动点击', 'error');

            // 列出页面上所有的按钮供调试
            const allButtons = document.querySelectorAll('button');
            log(`页面上共有 ${allButtons.length} 个按钮:`, 'debug');
            allButtons.forEach((btn, index) => {
                const text = (btn.textContent || btn.innerText || '').trim();
                const type = btn.type || 'button';
                const disabled = btn.disabled;
                const visible = btn.offsetParent !== null;
                log(`按钮 ${index + 1}: "${text}" (type: ${type}, disabled: ${disabled}, visible: ${visible})`, 'debug');
            });
        }

        return false;
    }

    // 处理验证后的登录页面 - 修改为使用随机密码，支持中英文
    async function handleGitHubLoginAfterVerify() {
        log('处理验证后的登录页面');
        updateStatus('🔐 准备填写GitHub账号信息', 'processing');

        // 等待页面加载
        await sleep(2000);

        const email = GM_getValue('github_email');
        const username = GM_getValue('github_username');
        const password = GM_getValue('github_password', ''); // 从存储中读取随机密码

        // 查找登录表单
        const loginForm = document.querySelector('form[action="/session"]');
        if (loginForm) {
            updateStatus('📝 填写登录信息', 'processing');

            // 先填写邮箱（而不是用户名）
            const loginInput = await waitForElement('#login_field, input[name="login"]', 5000);
            if (loginInput) {
                // 使用邮箱登录
                directFillInput(loginInput, email);
                updateStatus(`填写邮箱: ${email}`, 'success');

                // 等待邮箱填写完成
                await sleep(200);
            }

            // 填写密码
            const passwordInput = await waitForElement('#password, input[type="password"]', 5000);
            if (passwordInput) {
                directFillInput(passwordInput, password);
                updateStatus('填写随机密码', 'success');

                // 等待密码填写完成
                await sleep(200);
            }

            // 确保所有输入都完成
            updateStatus('✅ 账号信息已填写完成！', 'success');
            updateStatus('🎉 注册流程完成！请手动点击登录按钮', 'success');
            updateStatus('📌 账号信息已保存在账号库中', 'success');

            // 更新账号库中的状态
            const accountId = GM_getValue('current_account_id');
            if (accountId) {
                updateAccountStatus(accountId, true);
            }

            // 清理状态
            cleanupRegistrationState();

            return true;
        }

        return false;
    }

    // 清理注册状态
    function cleanupRegistrationState() {
        GM_setValue('registration_state', 'complete');
        GM_deleteValue('current_account_id');
        // 保留账号信息供用户查看
        // GM_deleteValue('github_email');
        // GM_deleteValue('github_username');
        // GM_deleteValue('github_password');
    }

    // ======================== 初始化 ========================

    function init() {
        log('脚本初始化 v3.8 - 智能仿人类账号生成');
        updateStatus('🚀 GitHub自动注册脚本 v3.8 已启动', 'success');

        // 创建控制面板
        const panel = createControlPanel();

        // 绑定开始按钮
        const startBtn = panel.querySelector('.start-btn');
        startBtn.addEventListener('click', async () => {
            startBtn.disabled = true;
            startBtn.textContent = '运行中...';

            // 重置状态为initial
            GM_setValue('registration_state', 'github_signup');

            // 直接跳转到GitHub注册页面
            updateStatus('跳转到GitHub注册页面', 'processing');
            window.location.href = 'https://github.com/signup';
        });

        // 绑定重置按钮
        const resetBtn = panel.querySelector('.reset-btn');
        resetBtn.addEventListener('click', () => {
            if (confirm('确定要重置状态吗？这将清除当前的注册进度。')) {
                // 重置所有状态
                GM_setValue('registration_state', 'initial');
                GM_deleteValue('current_account_id');
                GM_deleteValue('github_email');
                GM_deleteValue('github_username');
                GM_deleteValue('github_password');

                updateStatus('✅ 状态已重置', 'success');
                updateStatus('📍 可以开始新的注册流程', 'success');

                // 更新UI
                panel.querySelector('.info-section').style.display = 'none';
                startBtn.disabled = false;
                startBtn.textContent = '开始注册';

                // 如果在注册页面，1秒后自动开始
                if (window.location.href.includes('anyrouter.top/register')) {
                    setTimeout(() => {
                        updateStatus('🎯 准备自动开始注册...', 'processing');
                        setTimeout(() => startRegistration(), 2000);
                    }, 1000);
                }
            }
        });

        // 获取当前状态
        const state = GM_getValue('registration_state', 'initial');
        const currentUrl = window.location.href;

        log(`初始化时状态: ${state}, URL: ${currentUrl}`, 'debug');

        // 根据当前页面判断实际状态
        if (currentUrl.includes('github.com')) {
            // 在GitHub页面
            if (state !== 'initial' && state !== 'complete') {
                // 有未完成的流程，自动继续
                log(`检测到未完成的注册流程，自动继续: ${state}`);
                updateStatus(`📌 继续之前的注册流程: ${state}`, 'processing');
                setTimeout(() => startRegistration(), 2000);
            } else {
                updateStatus('⚠️ 当前在GitHub页面但没有进行中的注册', 'warning');
            }
        }

        // 显示当前状态信息
        if (state !== 'initial') {
            const email = GM_getValue('github_email', '-');
            const username = GM_getValue('github_username', '-');
            if (email !== '-' || username !== '-') {
                updateAccountInfo(email, username, true);
            }
            updateStatus(`📊 当前流程: ${state}`, 'processing');
        }

        // 在GitHub注册页面时自动开始
        if (currentUrl.includes('github.com/signup') && state === 'github_signup') {
            updateStatus('🎯 准备自动开始GitHub注册...', 'processing');
            setTimeout(() => startRegistration(), 2000);
        } else {
            // 其他情况下也检查一次
            setTimeout(() => startRegistration(), 1000);
        }
    }

    // 查找验证码输入框 - 支持中英文
    function findVerificationCodeInput() {
        // 检查所有输入框
        const allInputs = document.querySelectorAll('input');
        log(`页面上找到 ${allInputs.length} 个输入框`, 'debug');

        // 过滤出可能的验证码输入框
        const possibleCodeInputs = [];

        for (const input of allInputs) {
            // 跳过隐藏的输入框
            if (input.offsetParent === null) continue;

            // 跳过明确的非验证码输入框
            if (input.type === 'email' || input.type === 'password' || input.type === 'hidden' || input.type === 'submit') continue;
            if (input.name && (input.name.includes('email') || input.name.includes('password') || input.name.includes('username'))) continue;

            // 记录输入框信息
            const info = {
                type: input.type,
                name: input.name,
                id: input.id,
                placeholder: input.placeholder,
                maxLength: input.maxLength,
                className: input.className,
                value: input.value
            };
            log(`检查输入框: ${JSON.stringify(info)}`, 'debug');

            // 收集可能的验证码输入框
            if (input.type === 'text' || input.type === 'number' || input.type === 'tel' || !input.type) {
                possibleCodeInputs.push(input);
            }
        }

        // 如果有6-8个相同类型的输入框，可能是分开的验证码输入
        if (possibleCodeInputs.length >= 6 && possibleCodeInputs.length <= 8) {
            log(`找到 ${possibleCodeInputs.length} 个可能的验证码输入框（多框模式）`, 'success');
            return possibleCodeInputs;
        }

        // 否则查找单个验证码输入框 - 支持中英文
        for (const input of possibleCodeInputs) {
            // 通过各种属性判断
            if (input.name && (input.name.includes('code') ||
                              input.name.includes('otp') ||
                              input.name.includes('verify') ||
                              input.name.includes('验证') ||
                              input.name.includes('验证码'))) {
                log('通过name属性找到验证码输入框', 'success');
                return [input];
            }
            if (input.placeholder && (input.placeholder.includes('code') ||
                                     input.placeholder.includes('验证') ||
                                     input.placeholder.includes('Code') ||
                                     input.placeholder.includes('验证码') ||
                                     input.placeholder.includes('Verification') ||
                                     input.placeholder.includes('OTP'))) {
                log('通过placeholder找到验证码输入框', 'success');
                return [input];
            }
            if (input.maxLength && (input.maxLength === 6 || input.maxLength === 8)) {
                log('通过maxLength找到可能的验证码输入框', 'success');
                return [input];
            }
            if (input.autocomplete === 'one-time-code') {
                log('通过autocomplete找到验证码输入框', 'success');
                return [input];
            }
            if (input.id && (input.id.includes('code') ||
                            input.id.includes('verify') ||
                            input.id.includes('otp') ||
                            input.id.includes('验证'))) {
                log('通过id找到验证码输入框', 'success');
                return [input];
            }
        }

        log('未找到验证码输入框', 'warning');
        return null;
    }

    // 处理GitHub验证页面
    async function handleGitHubVerificationPage() {
        log('处理GitHub验证页面');

        // 避免重复处理
        if (window._verificationPageHandling) {
            return true;
        }
        window._verificationPageHandling = true;

        updateStatus('🔍 正在查找验证码输入框...', 'processing');

        // 等待页面加载
        await sleep(2000);

        // 查找验证码输入框
        const codeInputs = findVerificationCodeInput();

        if (codeInputs && codeInputs.length > 0) {
            updateStatus('✅ 找到验证码输入框！', 'success');
            await handleVerificationCodeInput(codeInputs);
            window._verificationPageHandling = false;
            return true;
        }

        // 检查是否有 Visual puzzle 按钮 - 支持中英文
        const buttons = document.querySelectorAll('button');
        let visualPuzzleButton = null;

        for (const button of buttons) {
            const buttonText = button.textContent || '';
            if (buttonText.includes('Visual puzzle') ||
                buttonText.includes('视觉谜题') ||
                buttonText.includes('图形验证') ||
                buttonText.includes('图像验证') ||
                buttonText.includes('Verify') ||
                buttonText.includes('验证') ||
                buttonText.includes('Puzzle') ||
                buttonText.includes('谜题')) {
                visualPuzzleButton = button;
                break;
            }
        }

        if (visualPuzzleButton) {
            updateStatus('⚠️ 检测到图形验证，请手动完成', 'warning');
            updateStatus('👆 请点击按钮并完成验证', 'warning');
            log('等待用户完成Visual Puzzle验证', 'info');

            // 持续检查是否已经完成图形验证
            let checkCount = 0;
            const checkInterval = setInterval(async () => {
                checkCount++;

                // 显示等待提示
                if (checkCount % 5 === 0) {
                    updateStatus(`⏳ 等待手动验证中... (${checkCount}秒)`, 'pending');
                }

                // 再次查找验证码输入框
                const foundCodeInputs = findVerificationCodeInput();

                if (foundCodeInputs && foundCodeInputs.length > 0) {
                    clearInterval(checkInterval);
                    updateStatus('✅ 手动验证完成！', 'success');
                    updateStatus('🚀 开始自动获取验证码', 'processing');
                    await handleVerificationCodeInput(foundCodeInputs);
                    window._verificationPageHandling = false;
                } else if (checkCount > 120) { // 120秒超时
                    clearInterval(checkInterval);
                    updateStatus('❌ 等待手动验证超时', 'error');
                    window._verificationPageHandling = false;
                }
            }, 1000);

            return true;
        }

        // 如果都没找到，显示当前页面状态
        updateStatus('⚠️ 未找到验证码输入框，3秒后重试...', 'warning');
        log(`当前URL: ${window.location.href}`, 'debug');

        setTimeout(() => {
            window._verificationPageHandling = false;
            handleGitHubVerificationPage();
        }, 3000);

        return true;
    }

    // 处理验证码输入
    async function handleVerificationCodeInput(codeInputs) {
        updateStatus('🔍 找到验证码输入框', 'success');
        log(`准备填写验证码，输入框数量: ${codeInputs.length}`, 'success');

        try {
            // 等待几秒确保邮件已发送
            updateStatus('⏳ 等待GitHub发送验证邮件...', 'processing');
            await sleep(3000);

            // 获取验证码
            const code = await waitForVerificationCode();

            if (code) {
                // 检查是否是多个输入框的情况（每个数字一个框）
                if (codeInputs.length >= 6 && codeInputs.length <= 8 && code.length === codeInputs.length) {
                    updateStatus(`📝 检测到${codeInputs.length}个输入框，分别填入...`, 'processing');

                    for (let i = 0; i < code.length; i++) {
                        const input = codeInputs[i];
                        const digit = code[i];

                        input.focus();
                        input.value = digit;

                        // 触发各种可能的事件
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                        input.dispatchEvent(new KeyboardEvent('keydown', { key: digit, code: `Digit${digit}`, bubbles: true }));
                        input.dispatchEvent(new KeyboardEvent('keypress', { key: digit, code: `Digit${digit}`, bubbles: true }));
                        input.dispatchEvent(new KeyboardEvent('keyup', { key: digit, code: `Digit${digit}`, bubbles: true }));

                        await sleep(100); // 每个输入之间短暂延迟

                        log(`在第 ${i+1} 个框中填入: ${digit}`, 'debug');
                        updateStatus(`填入第 ${i+1} 位: ${digit}`, 'processing');
                    }

                    // 最后一个输入框失焦
                    codeInputs[codeInputs.length - 1].blur();
                    updateStatus(`✅ 已分别填入验证码: ${code}`, 'success');

                } else {
                    // 单个输入框的情况
                    const codeInput = codeInputs[0];
                    updateStatus('📝 填入验证码...', 'processing');
                    codeInput.value = '';
                    codeInput.focus();
                    directFillInput(codeInput, code);
                    updateStatus(`✅ 已自动填写验证码: ${code}`, 'success');
                }

                // 查找并点击继续按钮
                await sleep(500);

                // 查找提交按钮
                let submitButton = null;
                const buttonSelectors = [
                    'button[type="submit"]',
                    'button.btn-primary',
                    'button[class*="primary"]',
                    'button:not([type])'
                ];

                for (const selector of buttonSelectors) {
                    const buttons = document.querySelectorAll(selector);
                    for (const button of buttons) {
                        if (button.offsetParent !== null) {
                            const buttonText = button.textContent || '';
                            if (buttonText.includes('继续') ||
                                buttonText.includes('Continue') ||
                                buttonText.includes('验证') ||
                                buttonText.includes('Verify') ||
                                buttonText.includes('提交') ||
                                buttonText.includes('Submit') ||
                                buttonText.includes('确认') ||
                                buttonText.includes('Confirm') ||
                                buttonText === '' || // 有时按钮没有文字
                                buttonText.trim() === '') {
                                submitButton = button;
                                break;
                            }
                        }
                    }
                    if (submitButton) break;
                }

                if (submitButton) {
                    updateStatus('🚀 自动点击提交按钮', 'processing');
                    await clickElement(submitButton);
                    GM_setValue('registration_state', 'github_login_after_verify');
                    updateStatus('✅ 验证码已提交', 'success');
                } else {
                    log('未找到提交按钮', 'error');
                    updateStatus('⚠️ 未找到提交按钮，请手动提交', 'warning');
                }
            } else {
                updateStatus('❌ 未能获取到验证码', 'error');
            }
        } catch (error) {
            updateStatus(`❌ 获取验证码失败: ${error.message}`, 'error');
            log(`错误详情: ${error.stack}`, 'error');
        }
    }

    // 处理OAuth授权页面
    async function handleOAuthAuthorizePage() {
        log('处理OAuth授权页面');

        // 查找授权按钮 - 支持中英文
        const authorizeSelectors = [
            'button[name="authorize"]',
            'button[type="submit"]',
            'button:contains("Authorize")',
            'button:contains("授权")',
            'input[type="submit"][value*="Authorize"]',
            'input[type="submit"][value*="授权"]'
        ];

        let authorizeButton = null;
        for (const selector of authorizeSelectors) {
            authorizeButton = await waitForElement(selector, 1000);
            if (authorizeButton) break;
        }

        // 如果通过选择器没找到，尝试通过文本查找
        if (!authorizeButton) {
            const buttons = document.querySelectorAll('button, input[type="submit"]');
            for (const button of buttons) {
                const buttonText = button.textContent || button.value || '';
                if (buttonText.includes('Authorize') ||
                    buttonText.includes('授权') ||
                    buttonText.includes('确认') ||
                    buttonText.includes('Confirm') ||
                    buttonText.includes('允许') ||
                    buttonText.includes('Allow')) {
                    authorizeButton = button;
                    break;
                }
            }
        }
        if (authorizeButton) {
            updateStatus('授权GitHub访问权限', 'processing');
            await clickElement(authorizeButton);
            GM_setValue('registration_state', 'complete');

            // 更新账号库中的状态
            const accountId = GM_getValue('current_account_id');
            if (accountId) {
                updateAccountStatus(accountId, true);
            }

            return true;
        }

        return false;
    }

    // ======================== 主控制逻辑 ========================

    async function startRegistration() {
        const currentUrl = window.location.href;
        const state = GM_getValue('registration_state', 'initial');

        log(`当前状态: ${state}, URL: ${currentUrl}`, 'debug');

        try {
            // GitHub相关页面
            if (currentUrl.includes('github.com')) {
                // GitHub OAuth登录页面 - 包括重定向后的页面
                if (currentUrl.includes('/login') ||
                    currentUrl.includes('oauth') ||
                    currentUrl.includes('return_to')) {
                    // 如果状态是github_login，处理登录页面
                    if (state === 'github_login' || state === 'initial') {
                        const handled = await handleGitHubLoginPage();
                        if (!handled && state === 'github_login') {
                            // 如果没有找到创建账号链接，可能已经在其他页面
                            log('未处理登录页面，检查其他可能', 'warning');
                        }
                    } else if (state === 'github_login_after_verify') {
                        // 验证后的登录
                        await handleGitHubLoginAfterVerify();
                    }
                }

                // 注册页面
                if (currentUrl.includes('/join') || currentUrl.includes('/signup')) {
                    if (state === 'github_signup' || state === 'github_login') {
                        await handleGitHubSignupPage();
                    }
                }

                // 验证页面 - 改进URL检测
                if (currentUrl.includes('/signup') ||
                    currentUrl.includes('/verify') ||
                    currentUrl.includes('account_verification')) {
                    // 持续检查验证页面，因为验证码输入框可能会动态出现
                    if (state === 'github_verify' || state === 'github_signup') {
                        await handleGitHubVerificationPage();
                    }
                }

                // OAuth授权页面
                if (currentUrl.includes('/oauth/authorize') && state === 'oauth_authorize') {
                    await handleOAuthAuthorizePage();
                }
            }

            // 检查是否完成
            if (state === 'complete' && currentUrl.includes('github.com')) {
                updateStatus('🎉 GitHub注册流程完成！', 'success');
                updateStatus('✅ GitHub账号注册成功！', 'success');
                cleanupRegistrationState();
            }

        } catch (error) {
            log(`处理过程出错: ${error}`, 'error');
            updateStatus(`错误: ${error.message}`, 'error');
        }
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
